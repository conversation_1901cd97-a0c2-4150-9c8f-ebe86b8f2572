#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
位置标定系统测试脚本
"""

import sys
import os
import json
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心类（不依赖UI）
from plus import WorkZone, ZonePosition, PositionCalibrationManager

def test_position_calibration_manager():
    """测试位置标定管理器"""
    print("=" * 60)
    print("位置标定系统测试")
    print("=" * 60)
    
    # 创建标定管理器
    manager = PositionCalibrationManager("test_zone_positions.json")
    
    # 测试1: 查看初始状态
    print("\n🧪 测试1: 初始状态")
    status = manager.get_all_zones_status()
    for zone_key, zone_info in status.items():
        print(f"  {zone_info['name']}: {zone_info['position_mm']:.1f}mm, 已标定: {zone_info['calibrated']}")
    
    # 测试2: 标定检测区一
    print("\n🧪 测试2: 标定检测区一")
    success = manager.calibrate_zone(WorkZone.DETECTION_1, 200.0)
    print(f"标定结果: {'成功' if success else '失败'}")
    
    position = manager.get_zone_position(WorkZone.DETECTION_1)
    if position:
        print(f"检测区一位置: {position.slide_position_mm:.1f}mm ({position.slide_position_percent:.1f}%)")
        print(f"标定时间: {position.calibration_time}")
    
    # 测试3: 标定检测区二
    print("\n🧪 测试3: 标定检测区二")
    manager.calibrate_zone(WorkZone.DETECTION_2, 400.0)
    
    # 测试4: 标定装配区
    print("\n🧪 测试4: 标定装配区")
    manager.calibrate_zone(WorkZone.ASSEMBLY_1, 600.0)
    manager.calibrate_zone(WorkZone.ASSEMBLY_2, 800.0)
    
    # 测试5: 查看所有标定状态
    print("\n🧪 测试5: 所有区域标定状态")
    status = manager.get_all_zones_status()
    for zone_key, zone_info in status.items():
        status_text = "✅ 已标定" if zone_info['calibrated'] else "❌ 未标定"
        print(f"  {zone_info['name']}: {zone_info['position_mm']:.1f}mm ({zone_info['position_percent']:.1f}%) - {status_text}")
    
    # 测试6: 导出配置
    print("\n🧪 测试6: 导出配置")
    export_data = {
        "export_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "slide_rail_range_mm": 1150,
        "zones": status
    }
    
    export_filename = "test_calibration_export.json"
    with open(export_filename, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, ensure_ascii=False, indent=2)
    print(f"配置已导出到: {export_filename}")
    
    # 测试7: 重新加载配置
    print("\n🧪 测试7: 重新加载配置")
    manager2 = PositionCalibrationManager("test_zone_positions.json")
    status2 = manager2.get_all_zones_status()
    
    print("重新加载后的状态:")
    for zone_key, zone_info in status2.items():
        status_text = "✅ 已标定" if zone_info['calibrated'] else "❌ 未标定"
        print(f"  {zone_info['name']}: {zone_info['position_mm']:.1f}mm - {status_text}")
    
    # 清理测试文件
    try:
        os.remove("test_zone_positions.json")
        os.remove(export_filename)
        print("\n🧹 测试文件已清理")
    except:
        pass
    
    print("\n✅ 位置标定系统测试完成!")

def test_zone_position_calculations():
    """测试区域位置计算"""
    print("\n" + "=" * 60)
    print("区域位置计算测试")
    print("=" * 60)
    
    slide_rail_range = 1150  # mm
    
    test_positions = [
        (0, "起始位置"),
        (287.5, "25%位置"),
        (575, "50%位置"),
        (862.5, "75%位置"),
        (1150, "终点位置")
    ]
    
    for position_mm, description in test_positions:
        position_percent = (position_mm / slide_rail_range) * 100
        print(f"{description}: {position_mm:.1f}mm = {position_percent:.1f}%")
    
    print("\n✅ 位置计算测试完成!")

def simulate_calibration_workflow():
    """模拟标定工作流程"""
    print("\n" + "=" * 60)
    print("标定工作流程模拟")
    print("=" * 60)
    
    manager = PositionCalibrationManager("workflow_test.json")
    
    # 模拟现场标定流程
    calibration_sequence = [
        (WorkZone.DETECTION_1, 250.0, "检测区一"),
        (WorkZone.DETECTION_2, 500.0, "检测区二"),
        (WorkZone.ASSEMBLY_1, 750.0, "装配区一号（残次品）"),
        (WorkZone.ASSEMBLY_2, 1000.0, "装配区二号（合格品）")
    ]
    
    print("🔧 开始现场标定流程...")
    
    for zone, position_mm, zone_name in calibration_sequence:
        print(f"\n📍 正在标定 {zone_name}...")
        print(f"   1. 手动移动滑轨到合适位置")
        print(f"   2. 当前滑轨位置: {position_mm:.1f}mm")
        print(f"   3. 保存位置...")
        
        success = manager.calibrate_zone(zone, position_mm)
        if success:
            print(f"   ✅ {zone_name} 标定成功!")
        else:
            print(f"   ❌ {zone_name} 标定失败!")
    
    print(f"\n📋 标定完成情况:")
    status = manager.get_all_zones_status()
    all_calibrated = True
    
    for zone_key, zone_info in status.items():
        if zone_info['calibrated']:
            print(f"  ✅ {zone_info['name']}: {zone_info['position_mm']:.1f}mm")
        else:
            print(f"  ❌ {zone_info['name']}: 未标定")
            all_calibrated = False
    
    if all_calibrated:
        print(f"\n🎉 所有区域标定完成！系统可以开始自动运行。")
    else:
        print(f"\n⚠️ 还有区域未完成标定，请继续标定。")
    
    # 清理测试文件
    try:
        os.remove("workflow_test.json")
    except:
        pass
    
    print("\n✅ 工作流程模拟完成!")

if __name__ == "__main__":
    test_position_calibration_manager()
    test_zone_position_calculations()
    simulate_calibration_workflow()
