# 滑轨编程控制使用说明

## 📋 **概述**

基于你提供的MovJExt指令文档，我已经为你的系统添加了完整的滑轨编程控制功能。现在你可以通过代码来精确控制滑轨移动，而不需要手动操作。

## 🎯 **核心指令实现**

### **MovJExt指令**
```python
# 标准格式
MovJExt(位置/角度, {SpeedE=速度%, AccE=加速度%, SYNC=1})

# 实际使用示例
MovJExt(100, {SpeedE=50, AccE=50, SYNC=1})  # 移动到100mm位置
```

### **SyncAll指令**
```python
SyncAll()  # 等待所有运动指令执行完毕
```

## 🔧 **新增编程控制功能**

### **1. 单点精确移动**

#### **UI界面：**
- 📍 **位置输入框**：输入目标位置（毫米）
- ⚡ **速度输入框**：输入运动速度（1-100%）
- ▶️ **执行移动按钮**：执行单次移动

#### **代码实现：**
```python
def move_slide_with_movj_ext(self, position_mm, speed_percent=50, accel_percent=50):
    """使用MovJExt指令移动滑轨"""
    cmd = f"MovJExt({position_mm}, {{SpeedE={speed_percent}, AccE={accel_percent}, SYNC=1}})"
    success = send_cmd(self.motion_socket, cmd, "SLIDE")
    
    if success:
        # 发送SyncAll确保完成
        sync_cmd = "SyncAll()"
        send_cmd(self.motion_socket, sync_cmd, "SLIDE")
        return True
    return False
```

### **2. 预设运动序列**

#### **往返运动**
- 🔄 **功能**：滑轨在预设路径上往返运动
- 📍 **路径**：0mm → 100mm → 200mm → 300mm → 200mm → 100mm → 0mm
- ⚡ **速度**：60%

```python
def _slide_reciprocate_sequence(self):
    positions = [0, 100, 200, 300, 200, 100, 0]
    for pos in positions:
        self.move_slide_with_movj_ext(pos, 60)
        time.sleep(0.5)  # 短暂停留
```

#### **多点巡航**
- 📍 **功能**：滑轨按不同速度访问多个点
- 🎯 **巡航点**：每个点有独立的位置和速度设置

```python
def _slide_multi_point_sequence(self):
    cruise_points = [
        (50, 40),    # (位置mm, 速度%)
        (150, 60),
        (250, 80),
        (350, 60),
        (200, 40),
        (100, 30),
        (0, 50)
    ]
    for pos, speed in cruise_points:
        self.move_slide_with_movj_ext(pos, speed)
        time.sleep(1.0)  # 在每个点停留1秒
```

### **3. 自定义序列编程**

#### **UI界面：**
- 📝 **序列输入框**：输入位置序列，用逗号分隔
- 🎬 **执行序列按钮**：执行自定义序列

#### **使用示例：**
```
输入: 0,50,100,150,200,100,0
结果: 滑轨依次移动到这些位置
```

#### **代码实现：**
```python
def execute_custom_sequence(self):
    sequence_text = self.slide_sequence_entry.get().strip()
    positions = [float(pos.strip()) for pos in sequence_text.split(',') if pos.strip()]
    
    for pos in positions:
        self.move_slide_with_movj_ext(pos, 50)
        time.sleep(0.3)
```

### **4. 协调运动控制**

#### **功能描述：**
滑轨与机械臂的协调运动，自动处理坐标变换

```python
def execute_coordinated_movement(self, slide_position_mm, robot_x, robot_y, robot_z, robot_r=0):
    # 1. 移动滑轨到目标位置
    self.move_slide_with_movj_ext(slide_position_mm, 50)
    
    # 2. 计算坐标偏移
    offset_x, offset_y, offset_z = self.slide_rail.calculate_coordinate_offset(position_percent)
    
    # 3. 移动机械臂到调整后的坐标
    adjusted_x = robot_x + offset_x
    robot_cmd = f"MoveJ({adjusted_x}, {robot_y}, {robot_z}, {robot_r})"
    send_cmd(self.motion_socket, robot_cmd, "COORD")
    
    # 4. 等待所有运动完成
    send_cmd(self.motion_socket, "SyncAll()", "COORD")
```

## 🚀 **使用步骤**

### **1. 基本设置**
1. **启动程序**：`python plus.py`
2. **连接设备**：点击"连接机器人"
3. **确认连接**：查看滑轨状态显示"已连接"

### **2. 单点移动**
1. **输入位置**：在位置输入框输入目标位置（如：100）
2. **设置速度**：在速度输入框输入速度百分比（如：50）
3. **执行移动**：点击"▶️ 执行移动"
4. **查看结果**：观察日志和滑轨实际移动

### **3. 序列运动**
1. **往返运动**：点击"🔄 往返运动"
2. **多点巡航**：点击"📍 多点巡航"
3. **自定义序列**：
   - 在序列输入框输入：`0,100,200,100,0`
   - 点击"🎬 执行序列"
4. **停止序列**：点击"⏹️ 停止序列"

## 📊 **指令格式详解**

### **MovJExt参数说明**
```python
MovJExt(位置/角度, OPTIONS)

# 必选参数：
位置/角度：
- 类型为关节：单位为度(°)
- 类型为直线：单位为毫米(mm)

# 可选参数：
OPTIONS = {
    SpeedE: 1-100,    # 运动速度比例
    AccE: 1-100,      # 运动加速度比例  
    SYNC: 0或1        # 同步标识，建议使用1
}
```

### **实际发送的指令示例**
```python
# 移动到100mm位置，速度50%，加速度50%，同步执行
"MovJExt(100, {SpeedE=50, AccE=50, SYNC=1})"

# 移动到200mm位置，速度80%，加速度60%
"MovJExt(200, {SpeedE=80, AccE=60, SYNC=1})"

# 等待所有运动完成
"SyncAll()"
```

## 🔍 **日志监控**

### **执行过程日志示例**
```
🤖 执行滑轨编程移动: 位置=100mm, 速度=50%
📤 发送指令: MovJExt(100, {SpeedE=50, AccE=50, SYNC=1})
⏳ 等待滑轨运动完成...
✅ 滑轨移动完成: 100mm

🔄 开始滑轨往返运动序列
🎯 移动到位置 1/7: 0mm
🎯 移动到位置 2/7: 100mm
🎯 移动到位置 3/7: 200mm
✅ 滑轨往返运动完成
```

## ⚙️ **高级配置**

### **修改默认参数**
```python
# 在代码中可以调整的参数
默认速度 = 50%        # SpeedE参数
默认加速度 = 50%      # AccE参数
停留时间 = 0.5秒      # 序列运动中的停留时间
同步模式 = SYNC=1     # 建议保持为1
```

### **自定义运动序列**
```python
# 可以在代码中定义更多预设序列
custom_sequences = {
    "快速扫描": [0, 50, 100, 150, 200],
    "精密定位": [(10, 30), (20, 30), (30, 30)],  # (位置, 速度)
    "工作循环": [0, 100, 200, 300, 200, 100, 0]
}
```

## ❗ **注意事项**

### **1. 安全提醒**
- ⚠️ 确保滑轨行程范围内无障碍物
- ⚠️ 首次使用时建议使用较低速度测试
- ⚠️ 确保急停按钮可正常使用

### **2. 参数限制**
- 📏 **位置范围**：根据实际滑轨行程设置
- ⚡ **速度范围**：1-100%
- 🚀 **加速度范围**：1-100%

### **3. 指令要求**
- 🔄 **SYNC参数**：建议始终使用SYNC=1
- ⏱️ **执行顺序**：等待前一个指令完成再发送下一个
- 🔗 **连接状态**：确保滑轨和机器人都已连接

## 🛠️ **故障排除**

### **常见问题**

#### **1. 滑轨不移动**
- 检查连接状态
- 运行诊断功能
- 确认指令格式正确

#### **2. 移动不精确**
- 检查位置参数
- 确认滑轨类型设置（关节/直线）
- 校准零点位置

#### **3. 序列中断**
- 检查位置是否超出范围
- 确认速度参数有效
- 查看详细错误日志

---

**版本**：V16.0 滑轨编程控制版  
**更新日期**：2025-08-06  
**核心功能**：MovJExt指令控制 + 序列编程 + 协调运动
