#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的滑轨控制功能测试
"""

# 模拟滑轨控制器类（简化版）
class SimpleSlideRailController:
    def __init__(self):
        self.range_mm = 1150  # 滑轨行程范围
        self.current_position_percent = 0.0
        self.current_position_mm = 0.0
    
    def percent_to_mm(self, percent):
        """将百分比位置转换为毫米位置"""
        return (percent / 100.0) * self.range_mm
    
    def calculate_coordinate_offset(self, rail_position_percent):
        """根据滑轨位置计算机械臂坐标偏移量"""
        rail_position_mm = self.percent_to_mm(rail_position_percent)
        offset_x = rail_position_mm
        offset_y = 0
        offset_z = 0
        return offset_x, offset_y, offset_z

def test_coordinate_transformation():
    """测试坐标变换功能"""
    print("🎯 滑轨坐标变换测试")
    print("=" * 50)
    
    slide_rail = SimpleSlideRailController()
    print(f"滑轨行程范围: {slide_rail.range_mm}mm")
    
    # 模拟视觉识别的目标坐标
    vision_targets = [
        (300, 100, 0),    # 目标1
        (350, 150, 45),   # 目标2
        (400, 200, -30),  # 目标3
    ]
    
    # 模拟不同滑轨位置
    rail_positions = [0, 25, 50, 75, 100]
    
    print("\n原始目标坐标 -> 不同滑轨位置下的调整坐标:")
    print("-" * 70)
    
    for target_x, target_y, target_r in vision_targets:
        print(f"\n目标坐标: ({target_x}, {target_y}, {target_r}°)")
        
        for rail_pos in rail_positions:
            # 计算坐标偏移
            offset_x, offset_y, offset_z = slide_rail.calculate_coordinate_offset(rail_pos)
            
            # 应用坐标变换
            adjusted_x = target_x + offset_x
            adjusted_y = target_y + offset_y
            
            rail_mm = slide_rail.percent_to_mm(rail_pos)
            print(f"  滑轨{rail_pos:3.0f}% ({rail_mm:6.1f}mm): ({adjusted_x:6.1f}, {adjusted_y:6.1f}, {target_r:4.0f}°)")
    
    print("\n✅ 坐标变换测试完成")

def test_position_conversion():
    """测试位置转换"""
    print("\n🔄 位置转换测试")
    print("=" * 50)
    
    slide_rail = SimpleSlideRailController()
    
    test_positions = [0, 10, 25, 50, 75, 90, 100]
    
    print("百分比 -> 毫米位置")
    print("-" * 30)
    for percent in test_positions:
        mm = slide_rail.percent_to_mm(percent)
        print(f"  {percent:3.0f}% -> {mm:6.1f}mm")
    
    print("\n✅ 位置转换测试完成")

if __name__ == "__main__":
    print("🚀 启动滑轨控制功能简单测试")
    print("滑轨行程: 1150mm")
    
    test_position_conversion()
    test_coordinate_transformation()
    
    print("\n🎉 所有测试完成!")
    print("\n📋 测试总结:")
    print("✅ 滑轨行程范围: 1150mm")
    print("✅ 位置转换功能正常")
    print("✅ 坐标变换功能正常")
    print("\n💡 说明:")
    print("- 滑轨0%位置 = 0mm，100%位置 = 1150mm")
    print("- 滑轨移动直接影响机械臂X坐标")
    print("- 坐标变换公式: 调整后X = 原始X + 滑轨位置(mm)")
