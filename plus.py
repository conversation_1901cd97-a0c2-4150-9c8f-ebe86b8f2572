# =================================================================================
#  机器人视觉控制系统 - V15.0 (双视觉检测版)
# =================================================================================
import customtkinter as ctk
import socket
import threading
import queue
import time
from PIL import Image, ImageTk
import os
import ctypes
import struct
from enum import Enum
from dataclasses import dataclass
from typing import Optional, Dict, List
import json

# --- 1. 全局配置 ---
ROBOT_IP = "***********"
PYTHON_PC_IP = "*************"
DASHBOARD_PORT = 29999
MOTION_PORT = 30003
VISION_SERVER_PORT = 6005
GRIPPER_IO_INDEX = 1
VISION_TRIGGER_PORT = 6006
VISION_TRIGGER_CMD = "TRIGGER"

# --- 滑轨控制配置 ---
SLIDE_RAIL_ID = 9  # Modbus设备ID
SLIDE_RAIL_BAUDRATE = 115200
SLIDE_RAIL_CONTROL_REG = 1000  # 控制寄存器地址
SLIDE_RAIL_STATUS_REG = 2000   # 状态寄存器地址
SLIDE_RAIL_POSITION_REG = 2001 # 位置寄存器地址
SLIDE_RAIL_RANGE_MM = 1150     # 滑轨行程范围(mm) - 实际测量值

# =================================================================================
# 位置标定系统数据结构
# =================================================================================

class WorkZone(Enum):
    """工作区域枚举"""
    DETECTION_1 = "detection_1"      # 检测区一
    DETECTION_2 = "detection_2"      # 检测区二
    ASSEMBLY_1 = "assembly_1"        # 装配区一号（残次品）
    ASSEMBLY_2 = "assembly_2"        # 装配区二号（合格品）

@dataclass
class ZonePosition:
    """区域位置数据"""
    zone_name: str                   # 区域名称
    slide_position_mm: float         # 滑轨位置(mm)
    slide_position_percent: float    # 滑轨位置(%)
    is_calibrated: bool = False      # 是否已标定
    calibration_time: str = ""       # 标定时间

class PositionCalibrationManager:
    """位置标定管理器"""

    def __init__(self, config_file="zone_positions.json"):
        self.config_file = config_file
        self.zone_positions: Dict[WorkZone, ZonePosition] = {}
        self.load_positions()

    def load_positions(self):
        """加载位置配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for zone_key, zone_data in data.items():
                        zone = WorkZone(zone_key)
                        self.zone_positions[zone] = ZonePosition(**zone_data)
            else:
                # 创建默认配置
                self._create_default_positions()
        except Exception as e:
            print(f"加载位置配置失败: {e}")
            self._create_default_positions()

    def _create_default_positions(self):
        """创建默认位置配置"""
        zone_names = {
            WorkZone.DETECTION_1: "检测区一",
            WorkZone.DETECTION_2: "检测区二",
            WorkZone.ASSEMBLY_1: "装配区一号（残次品）",
            WorkZone.ASSEMBLY_2: "装配区二号（合格品）"
        }

        for zone, name in zone_names.items():
            self.zone_positions[zone] = ZonePosition(
                zone_name=name,
                slide_position_mm=0.0,
                slide_position_percent=0.0,
                is_calibrated=False
            )

    def save_positions(self):
        """保存位置配置"""
        try:
            data = {}
            for zone, position in self.zone_positions.items():
                data[zone.value] = {
                    "zone_name": position.zone_name,
                    "slide_position_mm": position.slide_position_mm,
                    "slide_position_percent": position.slide_position_percent,
                    "is_calibrated": position.is_calibrated,
                    "calibration_time": position.calibration_time
                }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存位置配置失败: {e}")
            return False

    def calibrate_zone(self, zone: WorkZone, slide_position_mm: float) -> bool:
        """标定区域位置"""
        try:
            slide_position_percent = (slide_position_mm / SLIDE_RAIL_RANGE_MM) * 100

            if zone in self.zone_positions:
                self.zone_positions[zone].slide_position_mm = slide_position_mm
                self.zone_positions[zone].slide_position_percent = slide_position_percent
                self.zone_positions[zone].is_calibrated = True
                self.zone_positions[zone].calibration_time = time.strftime("%Y-%m-%d %H:%M:%S")

                return self.save_positions()
            return False
        except Exception as e:
            print(f"标定区域位置失败: {e}")
            return False

    def get_zone_position(self, zone: WorkZone) -> Optional[ZonePosition]:
        """获取区域位置"""
        return self.zone_positions.get(zone)

    def is_zone_calibrated(self, zone: WorkZone) -> bool:
        """检查区域是否已标定"""
        position = self.get_zone_position(zone)
        return position.is_calibrated if position else False

    def get_all_zones_status(self) -> Dict[str, Dict]:
        """获取所有区域状态"""
        status = {}
        for zone, position in self.zone_positions.items():
            status[zone.value] = {
                "name": position.zone_name,
                "position_mm": position.slide_position_mm,
                "position_percent": position.slide_position_percent,
                "calibrated": position.is_calibrated,
                "calibration_time": position.calibration_time
            }
        return status

# =================================================================================
# 双视觉检测系统数据结构
# =================================================================================

class DetectionStage(Enum):
    """检测阶段枚举"""
    IDLE = "idle"                    # 空闲状态
    DETECTION_1 = "detection_1"      # 检测区一（上部检测）
    DETECTION_2 = "detection_2"      # 检测区二（下部检测）
    SORTING = "sorting"              # 分拣阶段
    COMPLETED = "completed"          # 完成

class DetectionResult(Enum):
    """检测结果枚举"""
    OK = "OK"                        # 检测通过
    DEFECT = "DEFECT"               # 检测失败/残次品
    NO_DATA = "NO_DATA"             # 无数据
    ERROR = "ERROR"                 # 检测错误

@dataclass
class MaterialData:
    """物料数据结构"""
    material_id: str                 # 物料ID
    detection_1_result: Optional[DetectionResult] = None  # 检测区一结果
    detection_1_coords: Optional[tuple] = None            # 检测区一坐标 (x, y, r)
    detection_2_result: Optional[DetectionResult] = None  # 检测区二结果
    detection_2_coords: Optional[tuple] = None            # 检测区二坐标 (x, y, r)
    final_decision: Optional[str] = None                  # 最终分拣决策
    stage: DetectionStage = DetectionStage.IDLE          # 当前阶段

    def is_defective(self) -> bool:
        """判断是否为残次品"""
        # 任一检测失败就算残次品
        return (self.detection_1_result in [DetectionResult.DEFECT, DetectionResult.NO_DATA, DetectionResult.ERROR] or
                self.detection_2_result in [DetectionResult.DEFECT, DetectionResult.NO_DATA, DetectionResult.ERROR])

    def is_ready_for_sorting(self) -> bool:
        """判断是否准备好分拣"""
        return (self.detection_1_result is not None and
                self.detection_2_result is not None)

def send_cmd(sock, cmd, log_prefix="CMD"):
    try:
        full_cmd = cmd + "\n"
        print(f"[{log_prefix}] SND: {full_cmd.strip()}")
        sock.sendall(full_cmd.encode('utf-8'))
        response = sock.recv(1024).decode('utf-8').strip()
        print(f"[{log_prefix}] RCV: {response}")
        parts = response.split(',')
        error_id_str = parts[0]
        if error_id_str == '0': return True
        else:
            print(f"❌ 指令 '{cmd}' 失败，错误码: {error_id_str}")
            return False
    except socket.error as e:
        print(f"❌ 发送指令 '{cmd}' 时网络错误: {e}")
        return False

# =================================================================================
# 滑轨控制类
# =================================================================================
class SlideRailController:
    """滑轨控制器 - 基于Modbus协议"""

    def __init__(self, robot_socket=None):
        self.robot_socket = robot_socket
        self.current_position_percent = 0.0  # 当前位置百分比 (0-100)
        self.current_position_mm = 0.0       # 当前位置毫米数
        self.is_connected = False
        self.range_mm = SLIDE_RAIL_RANGE_MM  # 滑轨行程范围

    def connect(self):
        """连接滑轨控制器"""
        try:
            # 这里使用机器人的socket连接来发送Modbus指令
            if self.robot_socket:
                self.is_connected = True
                return True, "滑轨连接成功"
            else:
                return False, "机器人未连接，无法连接滑轨"
        except Exception as e:
            return False, f"滑轨连接失败: {str(e)}"

    def disconnect(self):
        """断开滑轨连接"""
        self.is_connected = False

    def percent_to_register_value(self, percent):
        """将百分比位置转换为寄存器值"""
        # 根据JODELL滑轨的换算公式
        return int((100 - percent) * 255 / 100.0)

    def register_value_to_percent(self, reg_value):
        """将寄存器值转换为百分比位置"""
        # 根据JODELL滑轨的换算公式
        return int((100 - reg_value * 100 / 255.0) + 0.5)

    def percent_to_mm(self, percent):
        """将百分比位置转换为毫米位置"""
        return (percent / 100.0) * self.range_mm

    def mm_to_percent(self, mm):
        """将毫米位置转换为百分比位置"""
        return (mm / self.range_mm) * 100.0

    def move_to_position(self, position_percent, speed=50, accel=30):
        """移动滑轨到指定位置百分比"""
        if not self.is_connected:
            return False, "滑轨未连接"

        try:
            # 限制位置范围
            position_percent = max(0, min(100, position_percent))

            # 方法1：使用MovJExt指令（推荐）
            # 如果滑轨配置为直线类型，直接使用毫米值
            position_mm = self.percent_to_mm(position_percent)
            movj_cmd = f"MovJExt({position_mm}, {{SpeedE={speed}, AccE={accel}, SYNC=1}})"

            print(f"[SLIDE] 发送滑轨控制指令: {movj_cmd}")
            success = send_cmd(self.robot_socket, movj_cmd, "SLIDE")

            if success:
                self.current_position_percent = position_percent
                self.current_position_mm = position_mm
                return True, f"滑轨移动到位置 {position_percent:.1f}% ({position_mm:.1f}mm)"
            else:
                # 方法2：如果MovJExt失败，尝试使用JODELL API
                print("[SLIDE] MovJExt失败，尝试使用JODELL API")
                return self._move_with_jodell_api(position_percent, speed)

        except Exception as e:
            return False, f"滑轨控制异常: {str(e)}"

    def _move_with_jodell_api(self, position_percent, speed=50):
        """使用JODELL API控制滑轨"""
        try:
            # 转换为寄存器值
            reg_value = self.percent_to_register_value(position_percent)

            # 使用Modbus控制
            modbus_create_cmd = f"ModbusRTUCreate({SLIDE_RAIL_ID},{SLIDE_RAIL_BAUDRATE},0,1,8,1,1)"
            success = send_cmd(self.robot_socket, modbus_create_cmd, "SLIDE")

            if success:
                # 发送位置控制指令
                write_cmd = f"ModbusWrite({SLIDE_RAIL_ID},{SLIDE_RAIL_CONTROL_REG},1,{reg_value})"
                success = send_cmd(self.robot_socket, write_cmd, "SLIDE")

                if success:
                    self.current_position_percent = position_percent
                    self.current_position_mm = self.percent_to_mm(position_percent)
                    return True, f"滑轨移动到位置 {position_percent:.1f}% (Modbus方式)"
                else:
                    return False, "Modbus写入失败"
            else:
                return False, "Modbus连接失败"

        except Exception as e:
            return False, f"JODELL API控制异常: {str(e)}"

    def get_current_position(self):
        """获取滑轨当前位置"""
        if not self.is_connected:
            return False, 0, "滑轨未连接"

        try:
            # 使用JODELL API读取滑轨位置
            # 注意：这里需要调用Lua函数，可能需要通过特殊方式调用
            # 暂时使用Modbus方式读取
            read_cmd = f"ModbusRead({SLIDE_RAIL_ID},{SLIDE_RAIL_POSITION_REG},1)"
            success = send_cmd(self.robot_socket, read_cmd, "SLIDE")

            if success:
                # 这里需要根据实际响应格式来解析位置值
                # 根据JODELL_GetPosition的实现：return math.floor( (100 - tab[1]* 100/ 255.0/256.0 ) + 0.5)
                # 暂时返回当前记录的位置，实际使用时需要解析Modbus响应
                return True, self.current_position_percent, f"当前位置: {self.current_position_percent:.1f}% ({self.current_position_mm:.1f}mm)"
            else:
                return False, 0, f"位置读取失败"

        except Exception as e:
            return False, 0, f"位置读取异常: {str(e)}"

    def calculate_coordinate_offset(self, rail_position_percent):
        """根据滑轨位置计算机械臂坐标偏移量"""
        # 滑轨沿X轴移动，计算X轴偏移量
        rail_position_mm = self.percent_to_mm(rail_position_percent)

        # 基于比赛文件分析的坐标对应关系：
        # 从比赛文件看，机械臂工作范围大约在X: 275-350mm左右
        # 假设滑轨0%位置对应机械臂基础坐标系的起始位置
        # 滑轨移动会改变机械臂的实际X坐标

        # 坐标变换：滑轨位置直接对应X轴偏移
        # 如果滑轨向正方向移动，机械臂的有效工作区域也向正方向偏移
        offset_x = rail_position_mm
        offset_y = 0  # Y轴不受滑轨影响
        offset_z = 0  # Z轴不受滑轨影响

        return offset_x, offset_y, offset_z

# =================================================================================
# 双视觉检测管理器
# =================================================================================
class VisionDetectionManager:
    """双视觉检测管理器"""

    def __init__(self, log_callback=None):
        self.log_callback = log_callback
        self.current_material: Optional[MaterialData] = None
        self.material_queue: List[MaterialData] = []  # 待处理物料队列
        self.completed_materials: List[MaterialData] = []  # 已完成物料
        self.detection_stage = DetectionStage.IDLE

    def log(self, message: str, color: str = "white"):
        """日志输出"""
        if self.log_callback:
            self.log_callback(message, color)
        else:
            print(message)

    def start_new_material(self, material_id: str = None) -> MaterialData:
        """开始处理新物料"""
        if material_id is None:
            material_id = f"MAT_{int(time.time())}"

        material = MaterialData(material_id=material_id)
        self.current_material = material
        self.detection_stage = DetectionStage.DETECTION_1

        self.log(f"🆕 开始处理新物料: {material_id}", "cyan")
        return material

    def process_vision_data(self, vision_data: str) -> Optional[MaterialData]:
        """处理视觉数据"""
        try:
            # 解析视觉数据格式: "CAM1,OK,100,200,0;image.jpg" 或 "CAM2,DEFECT,150,250,90;image.jpg"
            parts = vision_data.split(';')
            if len(parts) < 1:
                self.log("❌ 视觉数据格式错误", "red")
                return None

            coord_data = parts[0]
            image_path = parts[1] if len(parts) > 1 else ""

            # 解析坐标数据
            coord_parts = coord_data.split(',')
            if len(coord_parts) < 5:
                self.log(f"❌ 坐标数据格式错误: {coord_data}", "red")
                return None

            camera_id = coord_parts[0].strip()
            detection_result = coord_parts[1].strip()
            x = float(coord_parts[2])
            y = float(coord_parts[3])
            r = float(coord_parts[4])

            # 转换检测结果
            result_enum = DetectionResult.OK if detection_result == "OK" else DetectionResult.DEFECT

            # 如果没有当前物料，创建新物料
            if self.current_material is None:
                self.start_new_material()

            # 根据摄像头ID更新对应的检测结果
            if camera_id == "CAM1":
                self.current_material.detection_1_result = result_enum
                self.current_material.detection_1_coords = (x, y, r)
                self.current_material.stage = DetectionStage.DETECTION_1
                self.log(f"📸 检测区一完成: {detection_result} at ({x:.1f}, {y:.1f}, {r:.1f})",
                        "green" if result_enum == DetectionResult.OK else "orange")

            elif camera_id == "CAM2":
                self.current_material.detection_2_result = result_enum
                self.current_material.detection_2_coords = (x, y, r)
                self.current_material.stage = DetectionStage.DETECTION_2
                self.log(f"📸 检测区二完成: {detection_result} at ({x:.1f}, {y:.1f}, {r:.1f})",
                        "green" if result_enum == DetectionResult.OK else "orange")
            else:
                self.log(f"⚠️ 未知摄像头ID: {camera_id}", "orange")
                return None

            # 检查是否完成所有检测
            if self.current_material.is_ready_for_sorting():
                self.current_material.stage = DetectionStage.SORTING
                self._make_sorting_decision()

            return self.current_material

        except Exception as e:
            self.log(f"❌ 处理视觉数据时出错: {e}", "red")
            return None

    def _make_sorting_decision(self):
        """做出分拣决策"""
        if not self.current_material:
            return

        material = self.current_material

        if material.is_defective():
            material.final_decision = "DEFECTIVE"
            destination = "装配区一号（残次品）"
            color = "red"
        else:
            material.final_decision = "GOOD"
            destination = "装配区二号（合格品）"
            color = "green"

        material.stage = DetectionStage.COMPLETED
        self.log(f"✅ 分拣决策: {material.material_id} -> {destination}", color)

        # 移动到已完成队列
        self.completed_materials.append(material)
        self.current_material = None
        self.detection_stage = DetectionStage.IDLE

    def get_current_coords_for_pickup(self) -> Optional[tuple]:
        """获取当前需要抓取的坐标"""
        if not self.current_material:
            return None

        if self.current_material.stage == DetectionStage.DETECTION_1:
            return self.current_material.detection_1_coords
        elif self.current_material.stage == DetectionStage.DETECTION_2:
            return self.current_material.detection_2_coords

        return None

    def get_sorting_destination(self) -> Optional[str]:
        """获取分拣目标位置"""
        if (self.current_material and
            self.current_material.stage == DetectionStage.COMPLETED):
            return self.current_material.final_decision
        return None

    def get_status_summary(self) -> Dict[str, str]:
        """获取状态摘要"""
        return {
            "current_stage": self.detection_stage.value,
            "current_material": self.current_material.material_id if self.current_material else "无",
            "detection_1": str(self.current_material.detection_1_result.value) if self.current_material and self.current_material.detection_1_result else "未检测",
            "detection_2": str(self.current_material.detection_2_result.value) if self.current_material and self.current_material.detection_2_result else "未检测",
            "completed_count": str(len(self.completed_materials))
        }

# =================================================================================
# 传送带控制类
# =================================================================================
class ConveyorController:
    """传送带控制器"""

    def __init__(self, robot_socket=None):
        self.robot_socket = robot_socket
        self.is_connected = False
        self.is_running = False
        self.speed_percent = 50  # 传送带速度百分比
        self.conveyor_id = 0     # 传送带ID

    def connect(self):
        """连接传送带"""
        try:
            if self.robot_socket:
                self.is_connected = True
                return True, "传送带连接成功"
            else:
                return False, "机器人未连接，无法连接传送带"
        except Exception as e:
            return False, f"传送带连接失败: {str(e)}"

    def disconnect(self):
        """断开传送带连接"""
        self.stop()
        self.is_connected = False

    def start(self, speed_percent=None):
        """启动传送带"""
        if not self.is_connected:
            return False, "传送带未连接"

        try:
            if speed_percent is not None:
                self.speed_percent = max(1, min(100, speed_percent))

            # 启动传送带指令
            start_cmd = f"CnvStart({self.conveyor_id}, {self.speed_percent})"
            success = send_cmd(self.robot_socket, start_cmd, "CONV")

            if success:
                self.is_running = True
                return True, f"传送带启动成功，速度: {self.speed_percent}%"
            else:
                return False, "传送带启动失败"

        except Exception as e:
            return False, f"传送带启动异常: {str(e)}"

    def stop(self):
        """停止传送带"""
        if not self.is_connected:
            return False, "传送带未连接"

        try:
            # 停止传送带指令
            stop_cmd = f"CnvStop({self.conveyor_id})"
            success = send_cmd(self.robot_socket, stop_cmd, "CONV")

            if success:
                self.is_running = False
                return True, "传送带停止成功"
            else:
                return False, "传送带停止失败"

        except Exception as e:
            return False, f"传送带停止异常: {str(e)}"

    def set_speed(self, speed_percent):
        """设置传送带速度"""
        if not self.is_connected:
            return False, "传送带未连接"

        try:
            self.speed_percent = max(1, min(100, speed_percent))

            if self.is_running:
                # 如果传送带正在运行，重新启动以应用新速度
                return self.start(self.speed_percent)
            else:
                return True, f"传送带速度设置为: {self.speed_percent}%"

        except Exception as e:
            return False, f"设置传送带速度异常: {str(e)}"

class RobotControlApp(ctk.CTk):
    def __init__(self):
        # ... (无变化)
        super().__init__()
        self.title("机器人视觉控制系统 (MG400) - 双视觉检测版")
        self.geometry("1000x800")  # 增大窗口以容纳更多UI元素
        self.dashboard_socket = None
        self.motion_socket = None
        self.vision_queue = queue.Queue()
        self.is_robot_connected = False

        # 初始化滑轨控制器
        self.slide_rail = SlideRailController()

        # 初始化传送带控制器
        self.conveyor = ConveyorController()

        # 初始化双视觉检测管理器
        self.vision_manager = VisionDetectionManager(log_callback=self.log)

        # 初始化位置标定管理器
        self.position_manager = PositionCalibrationManager()

        # 标定模式状态
        self.calibration_mode = False

        # 标定位置变量
        self.detection_zone_position = None  # 检测区位置
        self.assembly_zone_position = None   # 配置区位置

        self.create_widgets()
        self.vision_thread = threading.Thread(target=self.vision_listener_thread, daemon=True)
        self.vision_thread.start()
        self.process_vision_queue()
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        try:
            is_admin = os.getuid() == 0
        except AttributeError:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
        if is_admin: self.log("✅ 程序正以【管理员权限】运行。", "lightgreen")
        else: self.log("⚠️ 程序正以【普通用户权限】运行。", "orange")

        # 初始化标定状态显示
        self.refresh_calibration_status()

        # 启动定时更新
        self.start_periodic_updates()

    def create_widgets(self):
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(padx=10, pady=10, fill="both", expand=True)

        # 配置网格权重
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(1, weight=2)  # 右侧占更多空间
        self.main_frame.grid_rowconfigure(0, weight=1)

        # 创建左侧滚动面板
        self.left_scrollable_frame = ctk.CTkScrollableFrame(self.main_frame, width=450, height=700)
        self.left_scrollable_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

        # 紧急停止按钮（置顶）
        emergency_frame = ctk.CTkFrame(self.left_scrollable_frame)
        emergency_frame.pack(pady=5, padx=10, fill="x")
        self.emergency_stop_btn = ctk.CTkButton(
            emergency_frame,
            text="🛑 紧急停止",
            command=self.emergency_stop,
            fg_color="red",
            hover_color="darkred",
            height=40,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.emergency_stop_btn.pack(pady=5, fill="x")
        
        # 机器人手动控制面板
        manual_control_frame = ctk.CTkFrame(self.left_scrollable_frame)
        manual_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(manual_control_frame, text="🤖 机器人手动控制", font=ctk.CTkFont(size=16, weight="bold")).grid(row=0, column=0, columnspan=3, pady=10)

        # XYZ轴控制
        btn_x_plus = ctk.CTkButton(manual_control_frame, text="X+", width=60)
        btn_x_minus = ctk.CTkButton(manual_control_frame, text="X-", width=60)
        btn_y_plus = ctk.CTkButton(manual_control_frame, text="Y+", width=60)
        btn_y_minus = ctk.CTkButton(manual_control_frame, text="Y-", width=60)
        btn_z_plus = ctk.CTkButton(manual_control_frame, text="Z+", width=60)
        btn_z_minus = ctk.CTkButton(manual_control_frame, text="Z-", width=60)

        btn_x_plus.grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        btn_x_minus.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        btn_y_plus.grid(row=2, column=0, padx=5, pady=5, sticky="ew")
        btn_y_minus.grid(row=2, column=1, padx=5, pady=5, sticky="ew")
        btn_z_plus.grid(row=3, column=0, padx=5, pady=5, sticky="ew")
        btn_z_minus.grid(row=3, column=1, padx=5, pady=5, sticky="ew")

        # 绑定点动事件
        btn_x_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("X+"))
        btn_x_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("X-"))
        btn_y_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("Y+"))
        btn_y_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("Y-"))
        btn_z_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("Z+"))
        btn_z_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("Z-"))

        for btn in [btn_x_plus, btn_x_minus, btn_y_plus, btn_y_minus, btn_z_plus, btn_z_minus]:
            btn.bind("<ButtonRelease-1>", self.stop_jog)

        # 夹爪/吸盘控制
        self.gripper_btn = ctk.CTkButton(manual_control_frame, text="🤏 闭合夹爪", command=self.toggle_gripper, width=120)
        self.gripper_btn.grid(row=1, column=2, padx=5, pady=5, sticky="ew")

        # 回原点按钮
        self.btn_home = ctk.CTkButton(manual_control_frame, text="🏠 回原点", command=self.go_home, width=120)
        self.btn_home.grid(row=2, column=2, padx=5, pady=5, sticky="ew")

        # 配置列权重
        manual_control_frame.grid_columnconfigure(0, weight=1)
        manual_control_frame.grid_columnconfigure(1, weight=1)
        manual_control_frame.grid_columnconfigure(2, weight=1)

        # 滑轨编程控制面板
        slide_control_frame = ctk.CTkFrame(self.left_scrollable_frame)
        slide_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(slide_control_frame, text="🛤️ 滑轨编程控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 滑轨位置显示
        position_display_frame = ctk.CTkFrame(slide_control_frame)
        position_display_frame.pack(pady=5, padx=10, fill="x")
        ctk.CTkLabel(position_display_frame, text="当前位置:", width=80).pack(side="left", padx=5)
        self.slide_position_label = ctk.CTkLabel(position_display_frame, text="未连接", text_color="gray")
        self.slide_position_label.pack(side="left", padx=5)

        # 滑轨TCP控制
        tcp_control_frame = ctk.CTkFrame(slide_control_frame)
        tcp_control_frame.pack(pady=5, padx=10, fill="x")

        ctk.CTkLabel(tcp_control_frame, text="TCP控制:", width=80).pack(side="left", padx=5)

        # 位置输入框
        self.slide_position_entry = ctk.CTkEntry(tcp_control_frame, placeholder_text="位置(mm)", width=100)
        self.slide_position_entry.pack(side="left", padx=5)

        # 移动按钮
        ctk.CTkButton(tcp_control_frame, text="▶️ 移动",
                     command=self.move_slide_tcp, width=80).pack(side="left", padx=5)

        # 滑轨诊断按钮
        diagnosis_frame = ctk.CTkFrame(slide_control_frame)
        diagnosis_frame.pack(pady=5, padx=10, fill="x")
        ctk.CTkButton(diagnosis_frame, text="🔍 诊断滑轨问题",
                     command=self.diagnose_slide_rail_issue, width=120).pack(side="left", padx=5)



        # 传送带控制面板
        conveyor_control_frame = ctk.CTkFrame(self.left_scrollable_frame)
        conveyor_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(conveyor_control_frame, text="🚚 传送带控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 传送带状态显示
        conveyor_status_frame = ctk.CTkFrame(conveyor_control_frame)
        conveyor_status_frame.pack(pady=5, padx=10, fill="x")
        ctk.CTkLabel(conveyor_status_frame, text="状态:", width=60).pack(side="left", padx=5)
        self.conveyor_status_label = ctk.CTkLabel(conveyor_status_frame, text="未连接", text_color="gray")
        self.conveyor_status_label.pack(side="left", padx=5)

        # 传送带控制按钮
        conveyor_buttons_frame = ctk.CTkFrame(conveyor_control_frame)
        conveyor_buttons_frame.pack(pady=5, padx=10, fill="x")

        # 启动/停止按钮
        self.conveyor_start_btn = ctk.CTkButton(conveyor_buttons_frame, text="▶️ 启动",
                                               command=self.start_conveyor, width=80)
        self.conveyor_start_btn.pack(side="left", padx=5)

        self.conveyor_stop_btn = ctk.CTkButton(conveyor_buttons_frame, text="⏹️ 停止",
                                              command=self.stop_conveyor, width=80)
        self.conveyor_stop_btn.pack(side="left", padx=5)

        # 速度控制
        speed_frame = ctk.CTkFrame(conveyor_control_frame)
        speed_frame.pack(pady=5, padx=10, fill="x")
        ctk.CTkLabel(speed_frame, text="速度:", width=60).pack(side="left", padx=5)

        self.conveyor_speed_slider = ctk.CTkSlider(speed_frame, from_=1, to=100,
                                                  command=self.on_conveyor_speed_change)
        self.conveyor_speed_slider.set(50)
        self.conveyor_speed_slider.pack(side="left", padx=5, fill="x", expand=True)

        self.conveyor_speed_label = ctk.CTkLabel(speed_frame, text="50%", width=40)
        self.conveyor_speed_label.pack(side="right", padx=5)

        # 位置标定面板
        calibration_frame = ctk.CTkFrame(self.left_scrollable_frame)
        calibration_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(calibration_frame, text="🎯 位置标定", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 标定按钮
        calibration_buttons_frame = ctk.CTkFrame(calibration_frame)
        calibration_buttons_frame.pack(pady=5, padx=10, fill="x")

        ctk.CTkButton(calibration_buttons_frame, text="📍 标定检测区",
                     command=self.calibrate_detection_zone, width=120).pack(side="left", padx=5)

        ctk.CTkButton(calibration_buttons_frame, text="🔧 标定配置区",
                     command=self.calibrate_assembly_zone, width=120).pack(side="left", padx=5)

        # 标定状态显示
        calibration_status_frame = ctk.CTkFrame(calibration_frame)
        calibration_status_frame.pack(pady=5, padx=10, fill="x")

        # 检测区状态
        detection_status_frame = ctk.CTkFrame(calibration_status_frame)
        detection_status_frame.pack(fill="x", pady=2)
        ctk.CTkLabel(detection_status_frame, text="检测区:", width=60).pack(side="left", padx=5)
        self.detection_zone_label = ctk.CTkLabel(detection_status_frame, text="未标定", text_color="orange")
        self.detection_zone_label.pack(side="left", padx=5)

        # 配置区状态
        assembly_status_frame = ctk.CTkFrame(calibration_status_frame)
        assembly_status_frame.pack(fill="x", pady=2)
        ctk.CTkLabel(assembly_status_frame, text="配置区:", width=60).pack(side="left", padx=5)
        self.assembly_zone_label = ctk.CTkLabel(assembly_status_frame, text="未标定", text_color="orange")
        self.assembly_zone_label.pack(side="left", padx=5)

        # 快速移动按钮
        quick_move_frame = ctk.CTkFrame(calibration_frame)
        quick_move_frame.pack(pady=5, padx=10, fill="x")

        ctk.CTkButton(quick_move_frame, text="🎯 移动到检测区",
                     command=self.move_to_detection_zone, width=120).pack(side="left", padx=5)

        ctk.CTkButton(quick_move_frame, text="🔧 移动到配置区",
                     command=self.move_to_assembly_zone, width=120).pack(side="left", padx=5)

        # 双视觉检测控制面板
        vision_control_frame = ctk.CTkFrame(self.left_scrollable_frame)
        vision_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(vision_control_frame, text="📸 双视觉检测控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 检测控制按钮
        detection_buttons_frame = ctk.CTkFrame(vision_control_frame)
        detection_buttons_frame.pack(pady=5, padx=5, fill="x")

        ctk.CTkButton(detection_buttons_frame, text="🆕 开始新物料检测",
                     command=self.start_new_material_detection, height=35).pack(pady=2, fill="x")

        # 手动触发按钮（并排）
        manual_trigger_frame = ctk.CTkFrame(detection_buttons_frame)
        manual_trigger_frame.pack(pady=2, fill="x")

        ctk.CTkButton(manual_trigger_frame, text="📸 触发检测区一",
                     command=lambda: self.trigger_vision_capture("CAM1")).pack(side="left", padx=2, fill="x", expand=True)
        ctk.CTkButton(manual_trigger_frame, text="📸 触发检测区二",
                     command=lambda: self.trigger_vision_capture("CAM2")).pack(side="right", padx=2, fill="x", expand=True)

        # 检测状态显示
        detection_status_frame = ctk.CTkFrame(vision_control_frame); detection_status_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(detection_status_frame, text="检测状态", font=ctk.CTkFont(size=14, weight="bold")).pack()

        self.detection_status_labels = {}
        status_items = [
            ("current_stage", "当前阶段"),
            ("current_material", "当前物料"),
            ("detection_1", "检测区一"),
            ("detection_2", "检测区二"),
            ("completed_count", "已完成数量")
        ]

        for key, label_text in status_items:
            frame = ctk.CTkFrame(detection_status_frame); frame.pack(fill="x", pady=1)
            ctk.CTkLabel(frame, text=f"{label_text}:", width=80).pack(side="left", padx=5)
            status_label = ctk.CTkLabel(frame, text="未知", text_color="gray")
            status_label.pack(side="left", padx=5)
            self.detection_status_labels[key] = status_label

        # 位置标定控制面板
        calibration_control_frame = ctk.CTkFrame(self.left_scrollable_frame); calibration_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(calibration_control_frame, text="位置标定系统", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 标定模式开关
        calibration_mode_frame = ctk.CTkFrame(calibration_control_frame); calibration_mode_frame.pack(pady=5, padx=5, fill="x")
        self.calibration_mode_switch = ctk.CTkSwitch(calibration_mode_frame, text="标定模式", command=self.toggle_calibration_mode)
        self.calibration_mode_switch.pack(pady=5)

        # 区域标定面板
        zones_frame = ctk.CTkFrame(calibration_control_frame); zones_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(zones_frame, text="区域位置标定", font=ctk.CTkFont(size=14, weight="bold")).pack()

        # 创建4个区域的标定控件
        self.zone_calibration_widgets = {}
        zone_configs = [
            (WorkZone.DETECTION_1, "检测区一", "lightblue"),
            (WorkZone.DETECTION_2, "检测区二", "lightgreen"),
            (WorkZone.ASSEMBLY_1, "装配区一号（残次品）", "lightcoral"),
            (WorkZone.ASSEMBLY_2, "装配区二号（合格品）", "lightgoldenrodyellow")
        ]

        for zone, zone_name, color in zone_configs:
            zone_frame = ctk.CTkFrame(zones_frame); zone_frame.pack(fill="x", pady=2, padx=5)

            # 区域名称标签
            name_label = ctk.CTkLabel(zone_frame, text=zone_name, width=120)
            name_label.pack(side="left", padx=5)

            # 位置显示标签
            position_label = ctk.CTkLabel(zone_frame, text="未标定", text_color="gray", width=80)
            position_label.pack(side="left", padx=5)

            # 移动到位置按钮
            move_btn = ctk.CTkButton(zone_frame, text="移动到此", width=70,
                                   command=lambda z=zone: self.move_to_zone(z))
            move_btn.pack(side="left", padx=2)

            # 保存位置按钮
            save_btn = ctk.CTkButton(zone_frame, text="保存位置", width=70,
                                   command=lambda z=zone: self.save_zone_position(z))
            save_btn.pack(side="left", padx=2)

            # 状态指示器
            status_label = ctk.CTkLabel(zone_frame, text="●", text_color="red", width=20)
            status_label.pack(side="right", padx=5)

            self.zone_calibration_widgets[zone] = {
                "position_label": position_label,
                "move_btn": move_btn,
                "save_btn": save_btn,
                "status_label": status_label
            }

        # 标定操作按钮
        calibration_actions_frame = ctk.CTkFrame(calibration_control_frame); calibration_actions_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkButton(calibration_actions_frame, text="刷新状态", command=self.refresh_calibration_status).pack(side="left", padx=5)
        ctk.CTkButton(calibration_actions_frame, text="导出配置", command=self.export_calibration_config).pack(side="left", padx=5)
        ctk.CTkButton(calibration_actions_frame, text="导入配置", command=self.import_calibration_config).pack(side="left", padx=5)

        connect_frame = ctk.CTkFrame(self.left_scrollable_frame); connect_frame.pack(pady=20, padx=10, fill="x")

        # 状态显示区域
        status_frame = ctk.CTkFrame(connect_frame); status_frame.pack(fill="x", pady=5)
        self.connect_label = ctk.CTkLabel(status_frame, text="机器人未连接", text_color="orange"); self.connect_label.pack(side="left", padx=10)
        self.slide_rail_label = ctk.CTkLabel(status_frame, text="滑轨未连接", text_color="orange"); self.slide_rail_label.pack(side="left", padx=10)

        # 控制按钮区域
        control_frame = ctk.CTkFrame(connect_frame); control_frame.pack(fill="x", pady=5)
        self.btn_connect = ctk.CTkButton(control_frame, text="🔗 连接机器人", command=self.handle_connect_button_click)
        self.btn_connect.pack(side="right", padx=10)

        self.auto_run_switch = ctk.CTkSwitch(control_frame, text="⚙️ 自动运行", onvalue=True, offvalue=False)
        self.auto_run_switch.pack(side="right", padx=10)

        # 右侧监控面板
        self.right_frame = ctk.CTkFrame(self.main_frame)
        self.right_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")

        # 监控界面标题
        monitor_title_frame = ctk.CTkFrame(self.right_frame)
        monitor_title_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(monitor_title_frame, text="📺 实时监控界面", font=ctk.CTkFont(size=18, weight="bold")).pack()

        # 任务进度显示
        progress_frame = ctk.CTkFrame(self.right_frame)
        progress_frame.pack(pady=5, padx=10, fill="x")
        ctk.CTkLabel(progress_frame, text="任务进度:", font=ctk.CTkFont(size=14, weight="bold")).pack(side="left", padx=5)
        self.progress_label = ctk.CTkLabel(progress_frame, text="等待开始", text_color="gray")
        self.progress_label.pack(side="left", padx=5)

        # 视觉图像显示
        image_frame = ctk.CTkFrame(self.right_frame)
        image_frame.pack(pady=5, padx=10, fill="both", expand=True)
        ctk.CTkLabel(image_frame, text="📷 视觉检测图像", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        self.image_display_label = ctk.CTkLabel(image_frame, text="[等待视觉软件发送图像...]",
                                              fg_color="gray20", height=300, corner_radius=10)
        self.image_display_label.pack(pady=10, padx=10, fill="both", expand=True)

        # 日志显示
        log_frame = ctk.CTkFrame(self.right_frame)
        log_frame.pack(pady=5, padx=10, fill="both", expand=True)

        log_title_frame = ctk.CTkFrame(log_frame)
        log_title_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(log_title_frame, text="📝 系统日志", font=ctk.CTkFont(size=14, weight="bold")).pack(side="left")
        ctk.CTkButton(log_title_frame, text="清空日志", command=self.clear_log, width=80).pack(side="right", padx=5)

        self.log_textbox = ctk.CTkTextbox(log_frame, state="disabled", height=250, font=ctk.CTkFont(size=12))
        self.log_textbox.pack(pady=5, padx=5, fill="both", expand=True)

    # (后续所有函数都无变化，为简洁省略)
    # ...
    def log(self, message, color="white"):
        self.log_textbox.configure(state="normal"); self.log_textbox.tag_config(f"tag_{color}", foreground=color); self.log_textbox.insert("end", f"{message}\n", f"tag_{color}"); self.log_textbox.configure(state="disabled"); self.log_textbox.see("end")
    def handle_connect_button_click(self):
        if not self.is_robot_connected:
            try:
                self.log(f"正在连接机器人 at {ROBOT_IP}...", "cyan"); self.dashboard_socket = socket.create_connection((ROBOT_IP, DASHBOARD_PORT), timeout=5); self.motion_socket = socket.create_connection((ROBOT_IP, MOTION_PORT), timeout=5)
                self.log("正在尝试使能机器人...", "yellow")
                if not send_cmd(self.dashboard_socket, "EnableRobot()", "DASH"): raise ConnectionError("机器人使能失败")
                self.log("机器人已使能，等待伺服系统稳定...", "yellow"); time.sleep(1)

                # 初始化滑轨连接
                self.slide_rail.robot_socket = self.dashboard_socket
                success, message = self.slide_rail.connect()
                if success:
                    self.log(f"✅ {message}", "green")
                    self.slide_rail_label.configure(text="滑轨已连接", text_color="green")
                else:
                    self.log(f"⚠️ {message}", "orange")
                    self.slide_rail_label.configure(text="滑轨连接失败", text_color="red")

                # 初始化传送带连接
                self.conveyor.robot_socket = self.dashboard_socket
                success, message = self.conveyor.connect()
                if success:
                    self.log(f"✅ {message}", "green")
                    self.conveyor_status_label.configure(text="已连接", text_color="green")
                    self.conveyor_start_btn.configure(state="normal")
                    self.conveyor_stop_btn.configure(state="disabled")
                else:
                    self.log(f"⚠️ {message}", "orange")
                    self.conveyor_status_label.configure(text="连接失败", text_color="red")

                self.is_robot_connected = True; self.log("✅ 机器人连接并使能成功!", "green"); self.connect_label.configure(text="机器人已连接", text_color="green"); self.btn_connect.configure(text="断开连接")
            except Exception as e:
                self.log(f"❌ 连接失败: {e}", "red")
                if self.dashboard_socket: self.dashboard_socket.close()
                if self.motion_socket: self.motion_socket.close()
        else:
            send_cmd(self.dashboard_socket, "DisableRobot()", "DASH");

            # 断开滑轨连接
            self.slide_rail.disconnect()
            self.log("🔌 滑轨已断开", "orange")
            self.slide_rail_label.configure(text="滑轨未连接", text_color="orange")

            if self.dashboard_socket: self.dashboard_socket.close();
            if self.motion_socket: self.motion_socket.close();
            self.is_robot_connected = False; self.log("🔌 机器人已断开。", "orange"); self.connect_label.configure(text="机器人未连接", text_color="orange"); self.btn_connect.configure(text="连接机器人")
    def trigger_vision_capture(self, camera_id=""):
        """触发视觉拍照"""
        camera_info = f" ({camera_id})" if camera_id else ""
        self.log(f"📸 发送拍照触发指令{camera_info}...", "yellow")
        try:
            with socket.create_connection((PYTHON_PC_IP, VISION_TRIGGER_PORT), timeout=3) as s:
                # 如果指定了摄像头ID，在触发指令中包含
                cmd_to_send = f"{VISION_TRIGGER_CMD}_{camera_id}\n" if camera_id else f"{VISION_TRIGGER_CMD}\n"
                s.sendall(cmd_to_send.encode('utf-8'))
                self.log(f"✅ 触发指令已发送成功{camera_info}。", "green")
        except socket.timeout:
            self.log(f"❌ 触发失败: 连接视觉软件({PYTHON_PC_IP}:{VISION_TRIGGER_PORT})超时。", "red")
        except Exception as e:
            self.log(f"❌ 触发失败: {e}", "red")

    def start_new_material_detection(self):
        """开始新物料检测"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        material = self.vision_manager.start_new_material()
        self.log(f"🆕 开始新物料检测流程: {material.material_id}", "cyan")
        self.update_detection_status()

        # 自动触发第一次检测
        self.trigger_vision_capture("CAM1")

    def update_detection_status(self):
        """更新检测状态显示"""
        status = self.vision_manager.get_status_summary()

        for key, label in self.detection_status_labels.items():
            if key in status:
                value = status[key]
                label.configure(text=value)

                # 根据状态设置颜色
                if key == "detection_1" or key == "detection_2":
                    if value == "OK":
                        label.configure(text_color="green")
                    elif value == "DEFECT":
                        label.configure(text_color="red")
                    elif value == "未检测":
                        label.configure(text_color="gray")
                elif key == "current_stage":
                    if value == "idle":
                        label.configure(text_color="gray")
                    elif value in ["detection_1", "detection_2"]:
                        label.configure(text_color="blue")
                    elif value == "sorting":
                        label.configure(text_color="orange")
                    elif value == "completed":
                        label.configure(text_color="green")
    def start_jog(self, axis_id):
        if not self.is_robot_connected: self.log("⚠️ 请先连接机器人", "orange"); return
        self.log(f"🤖 开始点动: {axis_id}", "cyan"); send_cmd(self.motion_socket, f"MoveJog({axis_id})", "MOT")
    def stop_jog(self, event=None):
        if not self.is_robot_connected: return
        self.log("🤖 停止点动", "cyan"); send_cmd(self.motion_socket, "MoveJog()", "MOT"); time.sleep(0.2)
    def go_home(self):
        if not self.is_robot_connected: self.log("⚠️ 请先连接机器人", "orange"); return
        self.log("🤖 正在移动到安全原点...");
        if send_cmd(self.motion_socket, "MoveJ(200, 0, 50, 0)", "MOT"):
            send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("✅ 已到达原点。")
        else: self.log("❌ 回原点失败。", "red")

    def toggle_gripper(self):
        """切换夹爪/吸盘状态"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        # 切换夹爪状态
        if hasattr(self, 'gripper_closed') and self.gripper_closed:
            # 当前是闭合状态，执行张开
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 0)", "MOT")
            self.gripper_btn.configure(text="🤏 闭合夹爪")
            self.gripper_closed = False
            self.log("👐 夹爪已张开", "cyan")
        else:
            # 当前是张开状态，执行闭合
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 1)", "MOT")
            self.gripper_btn.configure(text="👐 张开夹爪")
            self.gripper_closed = True
            self.log("🤏 夹爪已闭合", "cyan")



    def update_slide_position_display(self):
        """更新滑轨位置显示"""
        if self.slide_rail.is_connected:
            success, position_percent, position_msg = self.slide_rail.get_current_position()
            if success:
                position_mm = self.slide_rail.current_position_mm
                self.slide_position_label.configure(
                    text=f"{position_mm:.1f}mm ({position_percent:.1f}%)",
                    text_color="green"
                )
            else:
                self.slide_position_label.configure(text="读取失败", text_color="red")
        else:
            self.slide_position_label.configure(text="未连接", text_color="gray")

    def emergency_stop(self):
        """紧急停止"""
        self.log("🛑 紧急停止！", "red")

        # 停止机器人运动
        if self.is_robot_connected:
            try:
                send_cmd(self.motion_socket, "MoveJog()", "MOT")  # 停止点动
                send_cmd(self.dashboard_socket, "EmergencyStop()", "DASH")  # 紧急停止
                self.log("🤖 机器人已紧急停止", "red")
            except:
                pass

        # 停止滑轨运动
        if self.slide_rail.is_connected:
            try:
                # 这里可以添加滑轨紧急停止指令
                self.log("🛤️ 滑轨已紧急停止", "red")
            except:
                pass

    def clear_log(self):
        """清空日志"""
        self.log_textbox.configure(state="normal")
        self.log_textbox.delete("1.0", "end")
        self.log_textbox.configure(state="disabled")
        self.log("📝 日志已清空", "cyan")

    def start_periodic_updates(self):
        """启动定时更新"""
        self.update_all_status()
        self.after(1000, self.start_periodic_updates)  # 每秒更新一次

    def update_all_status(self):
        """更新所有状态显示"""
        # 更新滑轨位置显示
        self.update_slide_position_display()

        # 更新检测状态显示
        self.update_detection_status()

        # 更新进度显示
        if hasattr(self, 'progress_label'):
            if self.vision_manager.current_material:
                stage_text = {
                    DetectionStage.IDLE: "空闲",
                    DetectionStage.DETECTION_1: "检测区一检测中",
                    DetectionStage.DETECTION_2: "检测区二检测中",
                    DetectionStage.SORTING: "分拣中",
                    DetectionStage.COMPLETED: "已完成"
                }.get(self.vision_manager.current_material.stage, "未知")

                self.progress_label.configure(
                    text=f"{self.vision_manager.current_material.material_id} - {stage_text}",
                    text_color="blue"
                )
            else:
                self.progress_label.configure(text="等待开始", text_color="gray")

    def process_vision_queue(self):
        try:
            while not self.vision_queue.empty():
                message = self.vision_queue.get_nowait()
                self.log(f"📩 收到视觉数据包: {message}", "cyan")

                # 使用双视觉检测管理器处理数据
                material = self.vision_manager.process_vision_data(message)

                if material:
                    # 更新状态显示
                    self.update_detection_status()

                    # 显示图像（如果有）
                    parts = message.split(';')
                    if len(parts) > 1:
                        image_path = parts[1].strip()
                        self.show_image_from_path(image_path)

                    # 如果启用自动运行且机器人已连接，执行相应动作
                    if self.auto_run_switch.get() and self.is_robot_connected:
                        self.execute_detection_workflow(material)

        except queue.Empty:
            pass
        self.after(100, self.process_vision_queue)

    def execute_detection_workflow(self, material: MaterialData):
        """执行检测工作流程"""
        try:
            if material.stage == DetectionStage.DETECTION_1:
                # 检测区一完成，移动物料到检测区二
                if material.detection_1_coords:
                    x, y, r = material.detection_1_coords
                    self.log(f"🔄 检测区一完成，准备移动物料到检测区二", "yellow")

                    # 1. 移动滑轨到检测区一位置（抓取物料）
                    if self.move_to_zone_for_task(WorkZone.DETECTION_1):
                        # 2. 抓取物料
                        self.execute_pickup_action(x, y, r)

                        # 3. 移动滑轨到检测区二位置
                        if self.move_to_zone_for_task(WorkZone.DETECTION_2):
                            # 4. 放置物料到检测区二
                            self.execute_place_action()

                            # 5. 触发检测区二检测
                            time.sleep(1)  # 等待物料稳定
                            self.trigger_vision_capture("CAM2")
                        else:
                            self.log("❌ 移动到检测区二失败", "red")
                    else:
                        self.log("❌ 移动到检测区一失败", "red")

            elif material.stage == DetectionStage.SORTING:
                # 两次检测都完成，执行分拣
                coords = material.detection_2_coords or material.detection_1_coords
                if coords:
                    x, y, r = coords

                    # 根据分拣决策移动到对应装配区
                    target_zone = WorkZone.ASSEMBLY_2 if material.final_decision == "GOOD" else WorkZone.ASSEMBLY_1

                    # 1. 移动滑轨到检测区二位置（抓取物料）
                    if self.move_to_zone_for_task(WorkZone.DETECTION_2):
                        # 2. 抓取物料
                        self.execute_pickup_action(x, y, r)

                        # 3. 移动滑轨到目标装配区
                        if self.move_to_zone_for_task(target_zone):
                            # 4. 放置物料到装配区
                            self.execute_place_action()
                            self.log("✅ 分拣任务完成!", "green")
                        else:
                            self.log("❌ 移动到装配区失败", "red")
                    else:
                        self.log("❌ 移动到检测区二失败", "red")

        except Exception as e:
            self.log(f"❌ 执行检测工作流程时出错: {e}", "red")

    def execute_pickup_action(self, target_x, target_y, target_r):
        """执行抓取动作"""
        if not self.is_robot_connected:
            self.log("⚠️ 抓取失败：机器人未连接", "orange")
            return False

        pickup_z_high, pickup_z_low = 50, 10
        try:
            self.log(f"🤏 开始抓取物料 at ({target_x:.1f}, {target_y:.1f}, {target_r:.1f})", "cyan")

            # 张开夹爪/关闭吸盘
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 0)", "MOT")

            # 移动到目标上方
            send_cmd(self.motion_socket, f"MoveJ({target_x}, {target_y}, {pickup_z_high}, {target_r})", "MOT")
            send_cmd(self.dashboard_socket, "Sync()", "DASH")

            # 下降到抓取位置
            send_cmd(self.motion_socket, f"MoveL({target_x}, {target_y}, {pickup_z_low}, {target_r})", "MOT")
            send_cmd(self.dashboard_socket, "Sync()", "DASH")

            # 闭合夹爪/启动吸盘
            self.log("🤏 抓取: 闭合夹爪/启动吸盘", "cyan")
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 1)", "MOT")
            send_cmd(self.dashboard_socket, "Sync()", "DASH")

            # 抬升到安全高度
            send_cmd(self.motion_socket, f"MoveL({target_x}, {target_y}, {pickup_z_high}, {target_r})", "MOT")
            send_cmd(self.dashboard_socket, "Sync()", "DASH")

            self.log("✅ 物料抓取完成", "green")
            return True

        except Exception as e:
            self.log(f"❌ 执行抓取动作时出错: {e}", "red")
            return False

    def execute_place_action(self, place_x=0, place_y=0, place_z=50):
        """执行放置动作"""
        if not self.is_robot_connected:
            self.log("⚠️ 放置失败：机器人未连接", "orange")
            return False

        # 如果没有指定放置坐标，使用默认的中心位置
        if place_x == 0 and place_y == 0:
            place_x, place_y = 0, 0  # 滑轨中心位置的机器人坐标

        try:
            self.log(f"👐 开始放置物料 at ({place_x:.1f}, {place_y:.1f}, {place_z:.1f})", "cyan")

            # 移动到放置位置
            send_cmd(self.motion_socket, f"MoveJ({place_x}, {place_y}, {place_z}, 0)", "MOT")
            send_cmd(self.dashboard_socket, "Sync()", "DASH")

            # 张开夹爪/关闭吸盘
            self.log("👐 放置: 张开夹爪/关闭吸盘", "cyan")
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 0)", "MOT")
            send_cmd(self.dashboard_socket, "Sync()", "DASH")

            # 回到安全位置
            self.go_home()

            self.log("✅ 物料放置完成", "green")
            return True

        except Exception as e:
            self.log(f"❌ 执行放置动作时出错: {e}", "red")
            return False

    def execute_sorting_action(self, target_x, target_y, target_r, decision):
        """执行分拣动作"""
        if not self.is_robot_connected:
            self.log("⚠️ 分拣失败：机器人未连接", "orange")
            return

        # 获取滑轨当前位置并计算坐标偏移
        if self.slide_rail.is_connected:
            success, rail_position, position_msg = self.slide_rail.get_current_position()
            if success:
                self.log(f"📏 滑轨{position_msg}", "cyan")
                # 计算坐标偏移量
                offset_x, offset_y, _ = self.slide_rail.calculate_coordinate_offset(rail_position)

                # 应用坐标变换
                adjusted_target_x = target_x + offset_x
                adjusted_target_y = target_y + offset_y

                self.log(f"🔄 坐标变换: 原始({target_x:.1f}, {target_y:.1f}) -> 调整后({adjusted_target_x:.1f}, {adjusted_target_y:.1f})", "yellow")

                # 使用调整后的坐标
                target_x = adjusted_target_x
                target_y = adjusted_target_y
            else:
                self.log(f"⚠️ 滑轨位置读取失败，使用原始坐标: {position_msg}", "orange")
        else:
            self.log("⚠️ 滑轨未连接，使用原始坐标", "orange")

        # 根据分拣决策确定目标位置
        if decision == "GOOD":
            place_x, place_y, place_z = 300, -150, 50  # 装配区二号（合格品）
            self.log(f"✅ 执行合格品分拣到装配区二号", "green")
        else:
            place_x, place_y, place_z = 150, -150, 50  # 装配区一号（残次品）
            self.log(f"❌ 执行残次品分拣到装配区一号", "red")

        # 执行抓取和放置动作
        pickup_z_high, pickup_z_low = 50, 10
        try:
            # 抓取动作
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 0)", "MOT")
            send_cmd(self.motion_socket, f"MoveJ({target_x}, {target_y}, {pickup_z_high}, {target_r})", "MOT")
            send_cmd(self.dashboard_socket, "Sync()", "DASH")
            send_cmd(self.motion_socket, f"MoveL({target_x}, {target_y}, {pickup_z_low}, {target_r})", "MOT")
            send_cmd(self.dashboard_socket, "Sync()", "DASH")
            self.log("🤏 抓取: 闭合夹爪", "cyan")
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 1)", "MOT")
            send_cmd(self.dashboard_socket, "Sync()", "DASH")
            send_cmd(self.motion_socket, f"MoveL({target_x}, {target_y}, {pickup_z_high}, {target_r})", "MOT")
            send_cmd(self.dashboard_socket, "Sync()", "DASH")

            # 放置动作
            send_cmd(self.motion_socket, f"MoveJ({place_x}, {place_y}, {place_z}, 0)", "MOT")
            send_cmd(self.dashboard_socket, "Sync()", "DASH")
            self.log("👐 放置: 张开夹爪", "cyan")
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 0)", "MOT")
            send_cmd(self.dashboard_socket, "Sync()", "DASH")

            self.go_home()
            self.log("✅ 分拣任务完成!", "green")

        except Exception as e:
            self.log(f"❌ 机器人执行分拣动作时出错: {e}", "red")
    def show_image_from_path(self, image_path):
        max_retries = 5; retry_delay = 0.2
        for attempt in range(max_retries):
            if os.path.exists(image_path):
                try:
                    with Image.open(image_path) as image: image.verify()
                    with Image.open(image_path) as image:
                        image.thumbnail((self.image_display_label.winfo_width(), self.image_display_label.winfo_height()), Image.Resampling.LANCZOS)
                        ctk_image = ImageTk.PhotoImage(image)
                        self.image_display_label.configure(image=ctk_image, text=""); self.image_display_label.image = ctk_image
                        self.log(f"✅ 图像显示成功。(尝试第 {attempt + 1} 次)", "green"); return
                except (IOError, SyntaxError) as e: self.log(f"   - 第 {attempt + 1} 次尝试：文件不完整 ({e})，稍后重试...", "yellow"); time.sleep(retry_delay)
                except PermissionError: self.log(f"   - 第 {attempt + 1} 次尝试：文件被占用，稍后重试...", "yellow"); time.sleep(retry_delay)
                except Exception as e: self.log(f"❌ 显示图像时发生未知错误: {e}", "red"); return
            else: self.log(f"❌ 找不到图像文件: {image_path}", "red"); return
        self.log(f"❌ 图像加载失败：在 {max_retries} 次尝试后依然无法读取文件。", "red")
    def vision_listener_thread(self):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            try:
                s.bind(('0.0.0.0', VISION_SERVER_PORT)); s.listen()
                print(f"👂 视觉服务器已启动，正在监听端口 {VISION_SERVER_PORT}...")
                while True:
                    conn, addr = s.accept()
                    with conn:
                        print(f"🤝 视觉软件已连接: {addr}")
                        while True:
                            data = conn.recv(1024)
                            if not data: print("🔌 视觉软件已断开。"); break
                            self.vision_queue.put(data.decode('utf-8').strip())
            except OSError as e: print(f"❌ 端口 {VISION_SERVER_PORT} 绑定失败: {e}")
    def on_closing(self):
        if self.is_robot_connected:
            self.log("正在断开机器人连接..."); send_cmd(self.dashboard_socket, "DisableRobot()", "DASH")
            # 断开滑轨连接
            self.slide_rail.disconnect()
            # 断开传送带连接
            self.conveyor.disconnect()
            if self.dashboard_socket: self.dashboard_socket.close();
            if self.motion_socket: self.motion_socket.close()
        self.destroy()

    # =================================================================================
    # 传送带控制方法
    # =================================================================================

    def start_conveyor(self):
        """启动传送带"""
        if not self.conveyor.is_connected:
            self.log("⚠️ 传送带未连接", "orange")
            return

        speed = int(self.conveyor_speed_slider.get())
        success, message = self.conveyor.start(speed)

        if success:
            self.log(f"✅ {message}", "green")
            self.conveyor_status_label.configure(text="运行中", text_color="green")
            self.conveyor_start_btn.configure(state="disabled")
            self.conveyor_stop_btn.configure(state="normal")
        else:
            self.log(f"❌ 传送带启动失败: {message}", "red")

    def stop_conveyor(self):
        """停止传送带"""
        if not self.conveyor.is_connected:
            self.log("⚠️ 传送带未连接", "orange")
            return

        success, message = self.conveyor.stop()

        if success:
            self.log(f"✅ {message}", "green")
            self.conveyor_status_label.configure(text="已停止", text_color="orange")
            self.conveyor_start_btn.configure(state="normal")
            self.conveyor_stop_btn.configure(state="disabled")
        else:
            self.log(f"❌ 传送带停止失败: {message}", "red")

    def on_conveyor_speed_change(self, value):
        """传送带速度改变回调"""
        speed = int(value)
        self.conveyor_speed_label.configure(text=f"{speed}%")

        # 如果传送带正在运行，实时更新速度
        if self.conveyor.is_connected and self.conveyor.is_running:
            success, message = self.conveyor.set_speed(speed)
            if success:
                self.log(f"🔄 传送带速度调整为: {speed}%", "cyan")
            else:
                self.log(f"⚠️ 速度调整失败: {message}", "orange")

    # =================================================================================
    # 简化的TCP滑轨控制方法
    # =================================================================================

    def move_slide_tcp(self):
        """通过TCP控制滑轨移动"""
        if not self.is_robot_connected:
            self.log("⚠️ 机器人未连接", "orange")
            return

        try:
            # 获取输入位置
            position_text = self.slide_position_entry.get().strip()
            if not position_text:
                self.log("❌ 请输入目标位置", "red")
                return

            position_mm = float(position_text)

            self.log(f"🤖 TCP控制滑轨移动到: {position_mm}mm", "cyan")

            # 方法1：尝试MovJExt指令
            cmd = f"MovJExt({position_mm}, {{SpeedE=50, AccE=50, SYNC=1}})"
            self.log(f"📤 发送指令: {cmd}", "cyan")

            success = send_cmd(self.motion_socket, cmd, "SLIDE")

            if success:
                # 发送SyncAll确保完成
                self.log("⏳ 等待运动完成...", "yellow")
                send_cmd(self.motion_socket, "SyncAll()", "SLIDE")

                # 更新位置记录
                self.slide_rail.current_position_mm = position_mm
                self.slide_rail.current_position_percent = self.slide_rail.mm_to_percent(position_mm)

                self.log(f"✅ 滑轨移动完成: {position_mm}mm", "green")
                self.update_slide_position_display()
            else:
                self.log(f"❌ MovJExt指令失败，请检查滑轨配置", "red")
                self.log("💡 建议检查：", "yellow")
                self.log("  1. 滑轨是否在DobotStudio Pro中正确配置", "white")
                self.log("  2. 扩展轴类型是否设置为'直线'", "white")
                self.log("  3. 滑轨通信参数是否正确", "white")

        except ValueError:
            self.log("❌ 请输入有效的数值", "red")
        except Exception as e:
            self.log(f"❌ TCP控制异常: {str(e)}", "red")

    # =================================================================================
    # 位置标定方法
    # =================================================================================

    def calibrate_detection_zone(self):
        """标定检测区位置"""
        if not self.slide_rail.is_connected:
            self.log("⚠️ 滑轨未连接，无法标定", "orange")
            return

        try:
            # 记录当前滑轨位置作为检测区位置
            current_position = self.slide_rail.current_position_mm

            # 保存检测区位置（可以保存到配置文件）
            self.detection_zone_position = current_position

            self.log(f"✅ 检测区标定完成: {current_position:.1f}mm", "green")
            self.detection_zone_label.configure(text=f"{current_position:.1f}mm", text_color="green")

        except Exception as e:
            self.log(f"❌ 检测区标定失败: {str(e)}", "red")

    def calibrate_assembly_zone(self):
        """标定配置区位置"""
        if not self.slide_rail.is_connected:
            self.log("⚠️ 滑轨未连接，无法标定", "orange")
            return

        try:
            # 记录当前滑轨位置作为配置区位置
            current_position = self.slide_rail.current_position_mm

            # 保存配置区位置（可以保存到配置文件）
            self.assembly_zone_position = current_position

            self.log(f"✅ 配置区标定完成: {current_position:.1f}mm", "green")
            self.assembly_zone_label.configure(text=f"{current_position:.1f}mm", text_color="green")

        except Exception as e:
            self.log(f"❌ 配置区标定失败: {str(e)}", "red")

    def move_to_detection_zone(self):
        """移动到检测区"""
        if self.detection_zone_position is None:
            self.log("⚠️ 检测区未标定", "orange")
            return

        try:
            self.log(f"🎯 移动到检测区: {self.detection_zone_position:.1f}mm", "cyan")

            cmd = f"MovJExt({self.detection_zone_position}, {{SpeedE=50, AccE=50, SYNC=1}})"
            success = send_cmd(self.motion_socket, cmd, "SLIDE")

            if success:
                send_cmd(self.motion_socket, "SyncAll()", "SLIDE")
                self.slide_rail.current_position_mm = self.detection_zone_position
                self.log("✅ 已到达检测区", "green")
                self.update_slide_position_display()
            else:
                self.log("❌ 移动到检测区失败", "red")

        except Exception as e:
            self.log(f"❌ 移动到检测区异常: {str(e)}", "red")

    def move_to_assembly_zone(self):
        """移动到配置区"""
        if self.assembly_zone_position is None:
            self.log("⚠️ 配置区未标定", "orange")
            return

        try:
            self.log(f"🎯 移动到配置区: {self.assembly_zone_position:.1f}mm", "cyan")

            cmd = f"MovJExt({self.assembly_zone_position}, {{SpeedE=50, AccE=50, SYNC=1}})"
            success = send_cmd(self.motion_socket, cmd, "SLIDE")

            if success:
                send_cmd(self.motion_socket, "SyncAll()", "SLIDE")
                self.slide_rail.current_position_mm = self.assembly_zone_position
                self.log("✅ 已到达配置区", "green")
                self.update_slide_position_display()
            else:
                self.log("❌ 移动到配置区失败", "red")

        except Exception as e:
            self.log(f"❌ 移动到配置区异常: {str(e)}", "red")



    # =================================================================================
    # 滑轨控制辅助方法
    # =================================================================================

    def diagnose_slide_rail_issue(self):
        """诊断滑轨无法移动的问题"""
        self.log("🔍 开始诊断滑轨问题...", "cyan")

        # 检查连接状态
        if not self.slide_rail.is_connected:
            self.log("❌ 滑轨未连接", "red")
            return False

        # 检查机器人连接
        if not self.is_robot_connected:
            self.log("❌ 机器人未连接", "red")
            return False

        # 尝试不同的控制方法
        self.log("🧪 测试滑轨控制方法...", "yellow")

        # 方法1：MovJExt指令
        try:
            test_cmd = "MovJExt(10, {SpeedE=30, AccE=20, SYNC=1})"
            self.log(f"测试指令: {test_cmd}", "cyan")
            success = send_cmd(self.dashboard_socket, test_cmd, "SLIDE")
            if success:
                self.log("✅ MovJExt指令测试成功", "green")
                return True
            else:
                self.log("❌ MovJExt指令测试失败", "red")
        except Exception as e:
            self.log(f"❌ MovJExt测试异常: {str(e)}", "red")

        # 方法2：Modbus控制
        try:
            self.log("🔄 尝试Modbus控制方式...", "yellow")
            modbus_create = f"ModbusRTUCreate({SLIDE_RAIL_ID},{SLIDE_RAIL_BAUDRATE},0,1,8,1,1)"
            success = send_cmd(self.dashboard_socket, modbus_create, "SLIDE")
            if success:
                self.log("✅ Modbus连接成功", "green")

                # 尝试写入控制寄存器
                reg_value = 200  # 测试值
                write_cmd = f"ModbusWrite({SLIDE_RAIL_ID},{SLIDE_RAIL_CONTROL_REG},1,{reg_value})"
                success = send_cmd(self.dashboard_socket, write_cmd, "SLIDE")
                if success:
                    self.log("✅ Modbus写入测试成功", "green")
                    return True
                else:
                    self.log("❌ Modbus写入测试失败", "red")
            else:
                self.log("❌ Modbus连接失败", "red")
        except Exception as e:
            self.log(f"❌ Modbus测试异常: {str(e)}", "red")

        # 给出诊断建议
        self.log("💡 滑轨问题诊断建议:", "yellow")
        self.log("1. 检查滑轨是否正确连接到机器人控制器", "white")
        self.log("2. 确认滑轨在DobotStudio Pro中是否正确配置", "white")
        self.log("3. 检查滑轨控制器的电源和通信线路", "white")
        self.log("4. 确认滑轨类型设置（关节/直线）", "white")
        self.log("5. 检查Modbus设备ID和寄存器地址", "white")

        return False



    def get_slide_rail_status(self):
        """获取滑轨状态信息"""
        if not self.slide_rail.is_connected:
            return "滑轨未连接"

        success, position, message = self.slide_rail.get_current_position()
        if success:
            return f"滑轨位置: {position:.1f}% ({self.slide_rail.current_position_mm:.1f}mm)"
        else:
            return f"滑轨状态读取失败: {message}"

    # =================================================================================
    # 位置标定系统方法
    # =================================================================================

    def toggle_calibration_mode(self):
        """切换标定模式"""
        self.calibration_mode = self.calibration_mode_switch.get()
        mode_text = "开启" if self.calibration_mode else "关闭"
        self.log(f"🔧 标定模式已{mode_text}", "cyan")

        # 刷新标定状态显示
        self.refresh_calibration_status()

    def move_to_zone(self, zone: WorkZone):
        """移动滑轨到指定区域"""
        if not self.slide_rail.is_connected:
            self.log("⚠️ 滑轨未连接，无法移动", "orange")
            return

        position = self.position_manager.get_zone_position(zone)
        if not position or not position.is_calibrated:
            self.log(f"⚠️ {position.zone_name if position else zone.value}未标定，无法移动", "orange")
            return

        self.log(f"🛤️ 移动滑轨到{position.zone_name}...", "yellow")

        # 移动滑轨到指定位置
        success = self.slide_rail.move_to_position_mm(position.slide_position_mm)
        if success:
            self.log(f"✅ 滑轨已移动到{position.zone_name} ({position.slide_position_mm:.1f}mm)", "green")
        else:
            self.log(f"❌ 滑轨移动到{position.zone_name}失败", "red")

    def save_zone_position(self, zone: WorkZone):
        """保存当前滑轨位置为区域位置"""
        if not self.slide_rail.is_connected:
            self.log("⚠️ 滑轨未连接，无法保存位置", "orange")
            return

        # 获取当前滑轨位置
        success, current_position_mm, position_msg = self.slide_rail.get_current_position()
        if not success:
            self.log(f"❌ 获取滑轨位置失败: {position_msg}", "red")
            return

        # 保存位置
        zone_position = self.position_manager.get_zone_position(zone)
        zone_name = zone_position.zone_name if zone_position else zone.value

        if self.position_manager.calibrate_zone(zone, current_position_mm):
            self.log(f"✅ {zone_name}位置已保存: {current_position_mm:.1f}mm", "green")
            self.refresh_calibration_status()
        else:
            self.log(f"❌ 保存{zone_name}位置失败", "red")

    def refresh_calibration_status(self):
        """刷新标定状态显示"""
        for zone, widgets in self.zone_calibration_widgets.items():
            position = self.position_manager.get_zone_position(zone)
            if position:
                if position.is_calibrated:
                    # 已标定
                    widgets["position_label"].configure(
                        text=f"{position.slide_position_mm:.1f}mm",
                        text_color="green"
                    )
                    widgets["status_label"].configure(text="●", text_color="green")
                else:
                    # 未标定
                    widgets["position_label"].configure(
                        text="未标定",
                        text_color="gray"
                    )
                    widgets["status_label"].configure(text="●", text_color="red")

                # 根据标定模式启用/禁用按钮
                state = "normal" if self.calibration_mode else "disabled"
                widgets["move_btn"].configure(state=state)
                widgets["save_btn"].configure(state=state)

    def export_calibration_config(self):
        """导出标定配置"""
        try:
            status = self.position_manager.get_all_zones_status()
            export_data = {
                "export_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "slide_rail_range_mm": SLIDE_RAIL_RANGE_MM,
                "zones": status
            }

            filename = f"calibration_export_{int(time.time())}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            self.log(f"✅ 标定配置已导出到: {filename}", "green")
        except Exception as e:
            self.log(f"❌ 导出标定配置失败: {e}", "red")

    def import_calibration_config(self):
        """导入标定配置"""
        try:
            # 查找可导入的标定配置文件
            import_files = [f for f in os.listdir('.') if f.startswith('calibration_export_') and f.endswith('.json')]
            if not import_files:
                self.log("❌ 未找到可导入的标定配置文件", "red")
                return

            # 使用最新的导出文件
            latest_file = max(import_files, key=lambda f: os.path.getmtime(f))

            with open(latest_file, 'r', encoding='utf-8') as f:
                import_data = json.load(f)

            # 导入区域配置
            zones_data = import_data.get('zones', {})
            for zone_key, zone_data in zones_data.items():
                try:
                    zone = WorkZone(zone_key)
                    if zone_data.get('calibrated', False):
                        position_mm = zone_data.get('position_mm', 0.0)
                        self.position_manager.calibrate_zone(zone, position_mm)
                except ValueError:
                    continue

            self.refresh_calibration_status()
            self.log(f"✅ 标定配置已从 {latest_file} 导入", "green")

        except Exception as e:
            self.log(f"❌ 导入标定配置失败: {e}", "red")

    def move_to_zone_for_task(self, zone: WorkZone) -> bool:
        """任务执行时移动到指定区域（内部调用）"""
        if not self.slide_rail.is_connected:
            self.log("⚠️ 滑轨未连接，使用固定坐标", "orange")
            return True  # 允许继续执行，使用固定坐标

        position = self.position_manager.get_zone_position(zone)
        if not position or not position.is_calibrated:
            self.log(f"⚠️ {position.zone_name if position else zone.value}未标定，使用固定坐标", "orange")
            return True  # 允许继续执行，使用固定坐标

        # 移动滑轨到指定位置
        success = self.slide_rail.move_to_position_mm(position.slide_position_mm)
        if success:
            self.log(f"🛤️ 滑轨已移动到{position.zone_name}", "cyan")
            return True
        else:
            self.log(f"❌ 滑轨移动到{position.zone_name}失败", "red")
            return False

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    app = RobotControlApp()
    app.mainloop()